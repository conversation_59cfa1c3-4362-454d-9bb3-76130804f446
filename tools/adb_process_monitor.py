"""
ADB进程监控工具
通过adb命令获取当前手机所有启动中的前台、后台应用进程
"""
import argparse
import json
import os
import re
import subprocess
import sys
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

try:
    from core.logger import log
    from pages.base.detectors.detector_utils import DetectorUtils
except ImportError:
    # 如果导入失败，使用简单的日志输出
    class SimpleLogger:
        def info(self, msg): print(f"[INFO] {msg}")
        def error(self, msg): print(f"[ERROR] {msg}")
        def warning(self, msg): print(f"[WARNING] {msg}")
        def debug(self, msg): print(f"[DEBUG] {msg}")

    log = SimpleLogger()

    # 简单的ADB命令执行器
    class SimpleDetectorUtils:
        @staticmethod
        def execute_adb_command(command: List[str], timeout: int = 10) -> Tuple[bool, str]:
            try:
                result = subprocess.run(
                    command,
                    capture_output=True,
                    text=True,
                    timeout=timeout,
                    encoding='utf-8',
                    errors='ignore'
                )
                success = result.returncode == 0
                output = result.stdout if success else result.stderr
                return success, output
            except subprocess.TimeoutExpired:
                return False, "命令超时"
            except Exception as e:
                return False, str(e)

    DetectorUtils = SimpleDetectorUtils


class AdbProcessMonitor:
    """ADB进程监控器"""
    
    def __init__(self):
        """初始化进程监控器"""
        self.detector_utils = DetectorUtils()
        
    def get_all_processes(self) -> List[Dict[str, Any]]:
        """
        获取所有进程信息
        
        Returns:
            List[Dict[str, Any]]: 进程信息列表
        """
        try:
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "ps", "-A"], timeout=30
            )
            
            if not success:
                log.error(f"获取进程列表失败: {output}")
                return []
            
            processes = []
            lines = output.strip().split('\n')
            
            # 跳过标题行
            if len(lines) > 1:
                for line in lines[1:]:
                    process_info = self._parse_process_line(line)
                    if process_info:
                        processes.append(process_info)
            
            log.info(f"获取到 {len(processes)} 个进程")
            return processes
            
        except Exception as e:
            log.error(f"获取进程信息失败: {e}")
            return []
    
    def _parse_process_line(self, line: str) -> Optional[Dict[str, Any]]:
        """
        解析进程行信息
        
        Args:
            line: 进程信息行
            
        Returns:
            Optional[Dict[str, Any]]: 解析后的进程信息
        """
        try:
            # ps -A 输出格式: USER PID PPID VSZ RSS WCHAN ADDR S NAME
            parts = line.strip().split()
            if len(parts) >= 9:
                return {
                    'user': parts[0],
                    'pid': parts[1],
                    'ppid': parts[2],
                    'vsz': parts[3],  # 虚拟内存大小
                    'rss': parts[4],  # 物理内存大小
                    'wchan': parts[5],
                    'addr': parts[6],
                    'state': parts[7],
                    'name': ' '.join(parts[8:]),
                    'type': 'unknown'
                }
        except Exception as e:
            log.debug(f"解析进程行失败: {line}, 错误: {e}")
        return None
    
    def get_foreground_activities(self) -> List[Dict[str, Any]]:
        """
        获取前台Activity信息
        
        Returns:
            List[Dict[str, Any]]: 前台Activity列表
        """
        try:
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "dumpsys", "activity", "activities"], timeout=30
            )
            
            if not success:
                log.error(f"获取前台Activity失败: {output}")
                return []
            
            activities = []
            lines = output.split('\n')
            
            current_activity = None
            for line in lines:
                # 查找当前前台Activity
                if "mResumedActivity" in line or "mFocusedActivity" in line:
                    activity_match = re.search(r'ActivityRecord{[^}]*}\s+([^\s]+)', line)
                    if activity_match:
                        activity_name = activity_match.group(1)
                        package_name = activity_name.split('/')[0] if '/' in activity_name else activity_name
                        
                        activities.append({
                            'activity': activity_name,
                            'package': package_name,
                            'type': 'foreground',
                            'status': 'resumed' if 'mResumedActivity' in line else 'focused'
                        })
            
            log.info(f"获取到 {len(activities)} 个前台Activity")
            return activities
            
        except Exception as e:
            log.error(f"获取前台Activity失败: {e}")
            return []
    
    def get_app_processes(self, processes: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        分类应用进程为前台和后台
        
        Args:
            processes: 所有进程列表
            
        Returns:
            Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]: (前台进程, 后台进程)
        """
        foreground_activities = self.get_foreground_activities()
        foreground_packages = {activity['package'] for activity in foreground_activities}
        
        app_processes = []
        system_processes = []
        
        for process in processes:
            process_name = process['name']
            
            # 过滤掉系统进程，只保留应用进程
            if (process_name.startswith('com.') or 
                process_name.startswith('org.') or
                '.' in process_name and not process_name.startswith('[')):
                
                # 判断是否为前台进程
                is_foreground = any(pkg in process_name for pkg in foreground_packages)
                process['type'] = 'foreground' if is_foreground else 'background'
                app_processes.append(process)
            else:
                system_processes.append(process)
        
        foreground_processes = [p for p in app_processes if p['type'] == 'foreground']
        background_processes = [p for p in app_processes if p['type'] == 'background']
        
        log.info(f"应用进程分类: 前台 {len(foreground_processes)} 个, 后台 {len(background_processes)} 个")
        return foreground_processes, background_processes
    
    def get_process_memory_info(self, package_name: str) -> Dict[str, Any]:
        """
        获取指定包名的内存信息
        
        Args:
            package_name: 应用包名
            
        Returns:
            Dict[str, Any]: 内存信息
        """
        try:
            success, output = self.detector_utils.execute_adb_command(
                ["adb", "shell", "dumpsys", "meminfo", package_name], timeout=15
            )
            
            if not success:
                return {}
            
            memory_info = {}
            lines = output.split('\n')
            
            for line in lines:
                if 'TOTAL' in line and 'kB' in line:
                    parts = line.strip().split()
                    if len(parts) >= 2:
                        try:
                            memory_info['total_memory_kb'] = int(parts[1])
                            memory_info['total_memory_mb'] = round(int(parts[1]) / 1024, 2)
                        except ValueError:
                            pass
                        break
            
            return memory_info
            
        except Exception as e:
            log.debug(f"获取内存信息失败 {package_name}: {e}")
            return {}
    
    def format_process_info(self, processes: List[Dict[str, Any]], process_type: str) -> str:
        """
        格式化进程信息输出
        
        Args:
            processes: 进程列表
            process_type: 进程类型 (foreground/background)
            
        Returns:
            str: 格式化后的输出
        """
        if not processes:
            return f"\n📱 {process_type.upper()} 进程: 无"
        
        output = f"\n📱 {process_type.upper()} 进程 ({len(processes)} 个):\n"
        output += "=" * 80 + "\n"
        output += f"{'序号':<4} {'PID':<8} {'包名/进程名':<40} {'内存(MB)':<10} {'状态':<6}\n"
        output += "-" * 80 + "\n"
        
        for i, process in enumerate(processes, 1):
            pid = process['pid']
            name = process['name']
            state = process['state']
            
            # 获取内存信息
            memory_mb = "N/A"
            if '.' in name and not name.startswith('['):
                memory_info = self.get_process_memory_info(name)
                if memory_info.get('total_memory_mb'):
                    memory_mb = str(memory_info['total_memory_mb'])
            
            # 截断过长的包名
            display_name = name[:38] + ".." if len(name) > 40 else name
            
            output += f"{i:<4} {pid:<8} {display_name:<40} {memory_mb:<10} {state:<6}\n"
        
        return output
    
    def export_to_json(self, foreground_processes: List[Dict[str, Any]], 
                      background_processes: List[Dict[str, Any]], 
                      output_file: str) -> bool:
        """
        导出进程信息到JSON文件
        
        Args:
            foreground_processes: 前台进程列表
            background_processes: 后台进程列表
            output_file: 输出文件路径
            
        Returns:
            bool: 导出是否成功
        """
        try:
            export_data = {
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'foreground_count': len(foreground_processes),
                    'background_count': len(background_processes),
                    'total_count': len(foreground_processes) + len(background_processes)
                },
                'foreground_processes': foreground_processes,
                'background_processes': background_processes
            }
            
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            log.info(f"进程信息已导出到: {output_path}")
            return True
            
        except Exception as e:
            log.error(f"导出进程信息失败: {e}")
            return False
    
    def filter_processes(self, processes: List[Dict[str, Any]], 
                        keyword: str = None, 
                        min_memory_mb: float = None) -> List[Dict[str, Any]]:
        """
        过滤进程
        
        Args:
            processes: 进程列表
            keyword: 关键词过滤
            min_memory_mb: 最小内存过滤(MB)
            
        Returns:
            List[Dict[str, Any]]: 过滤后的进程列表
        """
        filtered = processes
        
        if keyword:
            filtered = [p for p in filtered if keyword.lower() in p['name'].lower()]
        
        if min_memory_mb:
            filtered_with_memory = []
            for process in filtered:
                memory_info = self.get_process_memory_info(process['name'])
                if memory_info.get('total_memory_mb', 0) >= min_memory_mb:
                    filtered_with_memory.append(process)
            filtered = filtered_with_memory
        
        return filtered


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ADB进程监控工具')
    parser.add_argument('--export', type=str, help='导出结果到JSON文件')
    parser.add_argument('--filter', type=str, help='按关键词过滤进程')
    parser.add_argument('--min-memory', type=float, help='按最小内存过滤(MB)')
    parser.add_argument('--foreground-only', action='store_true', help='只显示前台进程')
    parser.add_argument('--background-only', action='store_true', help='只显示后台进程')
    
    args = parser.parse_args()
    
    # 如果没有参数，显示帮助
    if len(sys.argv) == 1:
        log.info("📱 ADB进程监控工具")
        log.info("=" * 60)
        log.info("使用方法:")
        log.info("  python adb_process_monitor.py                    # 显示所有应用进程")
        log.info("  python adb_process_monitor.py --foreground-only  # 只显示前台进程")
        log.info("  python adb_process_monitor.py --background-only  # 只显示后台进程")
        log.info("  python adb_process_monitor.py --filter wechat    # 过滤包含wechat的进程")
        log.info("  python adb_process_monitor.py --min-memory 50    # 过滤内存大于50MB的进程")
        log.info("  python adb_process_monitor.py --export result.json # 导出结果到文件")
        log.info("")
        log.info("💡 功能特性:")
        log.info("- 自动区分前台和后台应用进程")
        log.info("- 显示进程PID、内存使用、运行状态")
        log.info("- 支持关键词和内存大小过滤")
        log.info("- 支持导出结果到JSON文件")
        return
    
    try:
        monitor = AdbProcessMonitor()
        
        # 获取所有进程
        log.info("🔍 正在获取设备进程信息...")
        all_processes = monitor.get_all_processes()
        
        if not all_processes:
            log.error("❌ 未能获取到进程信息，请检查设备连接")
            return
        
        # 分类前台和后台进程
        foreground_processes, background_processes = monitor.get_app_processes(all_processes)
        
        # 应用过滤器
        if args.filter or args.min_memory:
            log.info(f"🔍 应用过滤器: 关键词='{args.filter}', 最小内存={args.min_memory}MB")
            foreground_processes = monitor.filter_processes(
                foreground_processes, args.filter, args.min_memory
            )
            background_processes = monitor.filter_processes(
                background_processes, args.filter, args.min_memory
            )
        
        # 显示结果
        if not args.background_only:
            print(monitor.format_process_info(foreground_processes, "前台"))
        
        if not args.foreground_only:
            print(monitor.format_process_info(background_processes, "后台"))
        
        # 显示统计信息
        total_count = len(foreground_processes) + len(background_processes)
        print(f"\n📊 统计信息:")
        print(f"前台进程: {len(foreground_processes)} 个")
        print(f"后台进程: {len(background_processes)} 个")
        print(f"总计: {total_count} 个应用进程")
        
        # 导出结果
        if args.export:
            success = monitor.export_to_json(
                foreground_processes, background_processes, args.export
            )
            if success:
                print(f"✅ 结果已导出到: {args.export}")
            else:
                print(f"❌ 导出失败")
        
    except KeyboardInterrupt:
        log.info("\n👋 用户中断操作")
    except Exception as e:
        log.error(f"❌ 运行出错: {e}")


if __name__ == '__main__':
    # main()
    monitor = AdbProcessMonitor()
    monitor.get_all_processes()
